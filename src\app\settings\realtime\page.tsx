/**
 * Real-time Settings Page
 * Configure real-time notification and messaging preferences
 */

import { MainLayout } from "@/components/layout/MainLayout";
import { RealtimeSettingsClient } from "@/components/settings/RealtimeSettingsClient";
import { getCurrentUser } from "@/lib/utils/auth";
import { redirect } from "next/navigation";

export const metadata = {
  title: "Real-time Settings - HIFNF",
  description: "Configure your real-time notification and messaging preferences",
};

export default async function RealtimeSettingsPage() {
  const user = await getCurrentUser();

  if (!user) {
    redirect("/login");
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Real-time Settings</h1>
            <p className="mt-2 text-gray-600">
              Configure your real-time notification and messaging preferences
            </p>
          </div>

          {/* Settings Content */}
          <RealtimeSettingsClient />
        </div>
      </div>
    </MainLayout>
  );
}

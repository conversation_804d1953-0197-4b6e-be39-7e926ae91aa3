/**
 * Supabase Configuration for Real-time Features
 * This is used ONLY for real-time messaging and notifications
 * Primary data storage remains in MySQL
 */

export const supabaseConfig = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  
  // Real-time configuration
  realtime: {
    enabled: process.env.ENABLE_REALTIME === 'true',
    provider: process.env.REALTIME_PROVIDER || 'supabase',
    
    // Connection settings
    heartbeatIntervalMs: 30000,
    reconnectDelayMs: 1000,
    maxReconnectAttempts: 5,
    
    // Performance settings
    enablePresence: true,
    enableBroadcast: true,
    enablePostgresChanges: true,
  },
  
  // Tables used for real-time (minimal data)
  tables: {
    messages: 'messages_realtime',
    notifications: 'notifications_realtime',
    presence: 'user_presence',
  },
  
  // Security settings
  auth: {
    persistSession: false, // We use NextAuth for primary auth
    autoRefreshToken: true,
    detectSessionInUrl: false,
  },
};

// Validation
if (!supabaseConfig.url || !supabaseConfig.anonKey) {
  throw new Error('Missing Supabase configuration. Please check your environment variables.');
}

// Export individual configs for easier access
export const SUPABASE_URL = supabaseConfig.url;
export const SUPABASE_ANON_KEY = supabaseConfig.anonKey;
export const SUPABASE_SERVICE_ROLE_KEY = supabaseConfig.serviceRoleKey;
export const REALTIME_ENABLED = supabaseConfig.realtime.enabled;

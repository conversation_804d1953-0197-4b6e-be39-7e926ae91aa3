"use client";

import { SessionProvider as NextAuthSessionProvider } from "next-auth/react";
import { ReactNode } from "react";
import { GlobalSessionMonitor } from "./GlobalSessionMonitor";
import { Toaster } from "react-hot-toast";
import { WalletProvider } from "@/contexts/WalletContext";
import { NotificationProvider } from "@/contexts/NotificationContext";

interface SessionProviderProps {
  children: ReactNode;
}

export function SessionProvider({ children }: SessionProviderProps) {
  return (
    <NextAuthSessionProvider>
      <GlobalSessionMonitor>
        <WalletProvider>
          <NotificationProvider>
            {children}
            {/* Global toast notifications */}
            <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#fff',
                color: '#333',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
              },
              success: {
                style: {
                  background: '#ECFDF5',
                  border: '1px solid #10B981',
                  color: '#065F46',
                },
              },
              error: {
                style: {
                  background: '#FEF2F2',
                  border: '1px solid #EF4444',
                  color: '#991B1B',
                },
              },
            }}
          />
        </WalletProvider>
      </GlobalSessionMonitor>
    </NextAuthSessionProvider>
  );
}

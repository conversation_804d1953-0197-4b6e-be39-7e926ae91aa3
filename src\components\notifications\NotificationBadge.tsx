"use client";

import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { useNotifications } from '@/contexts/NotificationContext';

interface NotificationBadgeProps {
  count: number;
  maxCount?: number;
  className?: string;
  animate?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'red' | 'blue' | 'green' | 'yellow' | 'purple';
}

export function NotificationBadge({ 
  count, 
  maxCount = 99, 
  className,
  animate = true,
  size = 'md',
  color = 'red'
}: NotificationBadgeProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [prevCount, setPrevCount] = useState(count);

  useEffect(() => {
    if (count > 0) {
      setIsVisible(true);
    } else {
      // Delay hiding to allow exit animation
      const timer = setTimeout(() => setIsVisible(false), 200);
      return () => clearTimeout(timer);
    }
  }, [count]);

  useEffect(() => {
    if (count > prevCount && animate) {
      // Trigger animation when count increases
      const badge = document.querySelector('.notification-badge');
      if (badge) {
        badge.classList.add('animate-bounce');
        setTimeout(() => {
          badge.classList.remove('animate-bounce');
        }, 600);
      }
    }
    setPrevCount(count);
  }, [count, prevCount, animate]);

  if (!isVisible && count === 0) return null;

  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();

  const sizeClasses = {
    sm: 'h-4 w-4 text-xs',
    md: 'h-5 w-5 text-xs',
    lg: 'h-6 w-6 text-sm'
  };

  const colorClasses = {
    red: 'bg-red-500 text-white',
    blue: 'bg-blue-500 text-white',
    green: 'bg-green-500 text-white',
    yellow: 'bg-yellow-500 text-black',
    purple: 'bg-purple-500 text-white'
  };

  return (
    <span
      className={cn(
        "notification-badge absolute -top-1 -right-1 flex items-center justify-center rounded-full font-bold transition-all duration-300 transform",
        sizeClasses[size],
        colorClasses[color],
        count > 0 ? "scale-100 opacity-100" : "scale-0 opacity-0",
        animate && count > prevCount && "animate-pulse",
        className
      )}
      style={{
        minWidth: size === 'sm' ? '16px' : size === 'md' ? '20px' : '24px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
        border: '2px solid white'
      }}
    >
      {displayCount}
    </span>
  );
}

// Real-time notification badge that automatically updates
interface RealtimeNotificationBadgeProps {
  className?: string;
  animate?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'red' | 'blue' | 'green' | 'yellow' | 'purple';
  maxCount?: number;
  showZero?: boolean;
}

export function RealtimeNotificationBadge({
  className,
  animate = true,
  size = 'md',
  color = 'red',
  maxCount = 99,
  showZero = false,
}: RealtimeNotificationBadgeProps) {
  const { unreadCount, isConnected, isRealtimeEnabled } = useNotifications();

  // Use the existing NotificationBadge with real-time count
  return (
    <NotificationBadge
      count={unreadCount}
      maxCount={maxCount}
      className={cn(
        // Add opacity when not connected but real-time is enabled
        !isConnected && isRealtimeEnabled && 'opacity-50',
        className
      )}
      animate={animate}
      size={size}
      color={color}
    />
  );
}

// Notification bell icon with real-time badge
interface NotificationBellProps {
  className?: string;
  onClick?: () => void;
  size?: 'sm' | 'md' | 'lg';
  showStatus?: boolean;
}

export function NotificationBell({
  className,
  onClick,
  size = 'md',
  showStatus = false
}: NotificationBellProps) {
  const { unreadCount, isConnected, isRealtimeEnabled } = useNotifications();

  const iconSizes = {
    sm: 'h-5 w-5',
    md: 'h-6 w-6',
    lg: 'h-7 w-7',
  };

  return (
    <div className="relative">
      <button
        onClick={onClick}
        className={cn(
          'relative p-2 text-gray-600 hover:text-gray-900 transition-colors rounded-lg hover:bg-gray-100',
          className
        )}
        aria-label={`Notifications${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`}
      >
        {/* Bell Icon */}
        <svg
          className={iconSizes[size]}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
          />
        </svg>

        {/* Real-time Badge */}
        <RealtimeNotificationBadge size="sm" />
      </button>

      {/* Connection Status */}
      {showStatus && (
        <div className="absolute -bottom-1 -right-1">
          <div
            className={cn(
              'h-3 w-3 rounded-full border-2 border-white',
              isConnected ? 'bg-green-500' : isRealtimeEnabled ? 'bg-yellow-500' : 'bg-gray-400'
            )}
            title={
              isConnected
                ? 'Real-time connected'
                : isRealtimeEnabled
                ? 'Connecting...'
                : 'Real-time disabled'
            }
          />
        </div>
      )}
    </div>
  );
}

/**
 * Real-time Status Components
 * Shows connection status and real-time features status
 */

'use client';

import React from 'react';
import { useNotifications } from '@/contexts/NotificationContext';
import { useRealtimeMessages } from '@/hooks/useRealtimeMessages';
import { cn } from '@/lib/utils';

interface RealtimeStatusProps {
  className?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'dot' | 'badge' | 'full';
}

export function RealtimeStatus({
  className,
  showText = false,
  size = 'md',
  variant = 'dot',
}: RealtimeStatusProps) {
  const { isConnected: notificationsConnected, isRealtimeEnabled } = useNotifications();
  const { isConnected: messagesConnected } = useRealtimeMessages();

  // Overall connection status
  const isConnected = notificationsConnected && messagesConnected;

  if (!isRealtimeEnabled) {
    return variant === 'dot' ? (
      <div
        className={cn('h-2 w-2 rounded-full bg-gray-400', className)}
        title="Real-time disabled"
      />
    ) : showText ? (
      <span className={cn('text-xs text-gray-500', className)}>
        Real-time disabled
      </span>
    ) : null;
  }

  const sizeClasses = {
    sm: 'h-2 w-2',
    md: 'h-3 w-3',
    lg: 'h-4 w-4',
  };

  const statusColor = isConnected ? 'bg-green-500' : 'bg-yellow-500';
  const statusText = isConnected ? 'Connected' : 'Connecting...';

  if (variant === 'dot') {
    return (
      <div
        className={cn(
          'rounded-full',
          sizeClasses[size],
          statusColor,
          className
        )}
        title={`Real-time ${statusText.toLowerCase()}`}
      />
    );
  }

  if (variant === 'badge') {
    return (
      <div className={cn('flex items-center space-x-1', className)}>
        <div className={cn('rounded-full', sizeClasses[size], statusColor)} />
        {showText && (
          <span className="text-xs text-gray-600">{statusText}</span>
        )}
      </div>
    );
  }

  if (variant === 'full') {
    return (
      <div className={cn('flex items-center space-x-2 text-sm', className)}>
        <div className={cn('rounded-full', sizeClasses[size], statusColor)} />
        <div className="flex flex-col">
          <span className="text-xs font-medium text-gray-700">Real-time</span>
          <span className="text-xs text-gray-500">{statusText}</span>
        </div>
      </div>
    );
  }

  return null;
}

// Connection details component
interface RealtimeConnectionDetailsProps {
  className?: string;
}

export function RealtimeConnectionDetails({ className }: RealtimeConnectionDetailsProps) {
  const { isConnected: notificationsConnected, isRealtimeEnabled, unreadCount } = useNotifications();
  const { isConnected: messagesConnected, onlineUsers, typingUsers } = useRealtimeMessages();

  if (!isRealtimeEnabled) {
    return (
      <div className={cn('text-sm text-gray-500', className)}>
        Real-time features are disabled
      </div>
    );
  }

  return (
    <div className={cn('space-y-2 text-sm', className)}>
      <div className="flex items-center justify-between">
        <span className="text-gray-600">Notifications:</span>
        <div className="flex items-center space-x-2">
          <div className={cn(
            'h-2 w-2 rounded-full',
            notificationsConnected ? 'bg-green-500' : 'bg-red-500'
          )} />
          <span className={cn(
            'text-xs',
            notificationsConnected ? 'text-green-600' : 'text-red-600'
          )}>
            {notificationsConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <span className="text-gray-600">Messages:</span>
        <div className="flex items-center space-x-2">
          <div className={cn(
            'h-2 w-2 rounded-full',
            messagesConnected ? 'bg-green-500' : 'bg-red-500'
          )} />
          <span className={cn(
            'text-xs',
            messagesConnected ? 'text-green-600' : 'text-red-600'
          )}>
            {messagesConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
      </div>

      {notificationsConnected && (
        <div className="flex items-center justify-between">
          <span className="text-gray-600">Unread notifications:</span>
          <span className="text-xs font-medium text-blue-600">{unreadCount}</span>
        </div>
      )}

      {messagesConnected && (
        <>
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Online users:</span>
            <span className="text-xs font-medium text-green-600">{onlineUsers.length}</span>
          </div>

          {typingUsers.length > 0 && (
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Typing:</span>
              <span className="text-xs font-medium text-blue-600">{typingUsers.length} user(s)</span>
            </div>
          )}
        </>
      )}
    </div>
  );
}

// Real-time feature toggle
interface RealtimeToggleProps {
  className?: string;
  onToggle?: (enabled: boolean) => void;
}

export function RealtimeToggle({ className, onToggle }: RealtimeToggleProps) {
  const { isRealtimeEnabled } = useNotifications();

  const handleToggle = () => {
    // This would typically update a user preference
    // For now, we'll just call the callback
    onToggle?.(!isRealtimeEnabled);
  };

  return (
    <div className={cn('flex items-center space-x-3', className)}>
      <span className="text-sm font-medium text-gray-700">Real-time features</span>
      <button
        onClick={handleToggle}
        className={cn(
          'relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
          isRealtimeEnabled ? 'bg-blue-600' : 'bg-gray-200'
        )}
        role="switch"
        aria-checked={isRealtimeEnabled}
      >
        <span
          className={cn(
            'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
            isRealtimeEnabled ? 'translate-x-6' : 'translate-x-1'
          )}
        />
      </button>
      <RealtimeStatus size="sm" variant="badge" showText />
    </div>
  );
}

// Real-time stats component
export function RealtimeStats({ className }: { className?: string }) {
  const { unreadCount, recentNotifications, isConnected: notificationsConnected } = useNotifications();
  const { onlineUsers, typingUsers, isConnected: messagesConnected } = useRealtimeMessages();

  const stats = [
    {
      label: 'Unread Notifications',
      value: unreadCount,
      color: 'text-red-600',
      connected: notificationsConnected,
    },
    {
      label: 'Recent Notifications',
      value: recentNotifications.length,
      color: 'text-blue-600',
      connected: notificationsConnected,
    },
    {
      label: 'Online Users',
      value: onlineUsers.length,
      color: 'text-green-600',
      connected: messagesConnected,
    },
    {
      label: 'Typing Users',
      value: typingUsers.length,
      color: 'text-purple-600',
      connected: messagesConnected,
    },
  ];

  return (
    <div className={cn('grid grid-cols-2 gap-4', className)}>
      {stats.map((stat) => (
        <div
          key={stat.label}
          className="bg-white rounded-lg border border-gray-200 p-3"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-500">{stat.label}</p>
              <p className={cn('text-lg font-semibold', stat.color)}>
                {stat.value}
              </p>
            </div>
            <div className={cn(
              'h-2 w-2 rounded-full',
              stat.connected ? 'bg-green-500' : 'bg-gray-400'
            )} />
          </div>
        </div>
      ))}
    </div>
  );
}

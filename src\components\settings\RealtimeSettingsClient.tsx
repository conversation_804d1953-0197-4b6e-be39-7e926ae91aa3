/**
 * Real-time Settings Client Component
 * Manages real-time preferences and settings
 */

'use client';

import React, { useState } from 'react';
import { useNotifications } from '@/contexts/NotificationContext';
import { useRealtimeMessages } from '@/hooks/useRealtimeMessages';
import { RealtimeStatus, RealtimeConnectionDetails, RealtimeStats } from '@/components/realtime/RealtimeStatus';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';
import { 
  BellIcon, 
  ChatBubbleLeftRightIcon, 
  Cog6ToothIcon,
  CheckCircleIcon,
  XCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

export function RealtimeSettingsClient() {
  const {
    isConnected: notificationsConnected,
    isRealtimeEnabled,
    showToastNotifications,
    setShowToastNotifications,
    showBrowserNotifications,
    setShowBrowserNotifications,
    requestBrowserPermission,
    unreadCount,
    recentNotifications,
  } = useNotifications();

  const {
    isConnected: messagesConnected,
    onlineUsers,
    typingUsers,
    getConnectionStatus,
  } = useRealtimeMessages();

  const [isTestingNotifications, setIsTestingNotifications] = useState(false);

  // Test notification function
  const testNotification = async () => {
    setIsTestingNotifications(true);
    
    try {
      // Show toast notification
      if (showToastNotifications) {
        toast.success('🔔 Test notification - Toast notifications are working!', {
          duration: 4000,
          position: 'top-right',
        });
      }

      // Show browser notification
      if (showBrowserNotifications && 'Notification' in window && Notification.permission === 'granted') {
        new Notification('HIFNF - Test Notification', {
          body: 'Browser notifications are working correctly!',
          icon: '/favicon.ico',
          tag: 'test-notification',
        });
      }

      toast.success('Test notifications sent successfully!');
    } catch (error) {
      console.error('Failed to send test notification:', error);
      toast.error('Failed to send test notification');
    } finally {
      setIsTestingNotifications(false);
    }
  };

  // Request browser permission
  const handleRequestBrowserPermission = async () => {
    try {
      const granted = await requestBrowserPermission();
      if (granted) {
        toast.success('Browser notification permission granted!');
      } else {
        toast.error('Browser notification permission denied');
      }
    } catch (error) {
      console.error('Failed to request browser permission:', error);
      toast.error('Failed to request browser permission');
    }
  };

  return (
    <div className="space-y-8">
      {/* Connection Status Card */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Cog6ToothIcon className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Connection Status</h2>
            <p className="text-gray-600">Real-time system status and connection details</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Current Status</h3>
            <RealtimeConnectionDetails />
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Statistics</h3>
            <RealtimeStats />
          </div>
        </div>

        {!isRealtimeEnabled && (
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <InformationCircleIcon className="h-5 w-5 text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">
                Real-time features are currently disabled
              </span>
            </div>
            <p className="text-sm text-yellow-700 mt-1">
              Contact your administrator to enable real-time messaging and notifications.
            </p>
          </div>
        )}
      </div>

      {/* Notification Preferences */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-purple-100 rounded-lg">
            <BellIcon className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Notification Preferences</h2>
            <p className="text-gray-600">Configure how you receive notifications</p>
          </div>
        </div>

        <div className="space-y-6">
          {/* Toast Notifications */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Toast Notifications</h3>
              <p className="text-sm text-gray-600">Show popup notifications in the browser</p>
            </div>
            <button
              onClick={() => setShowToastNotifications(!showToastNotifications)}
              className={cn(
                'relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                showToastNotifications ? 'bg-blue-600' : 'bg-gray-200'
              )}
              role="switch"
              aria-checked={showToastNotifications}
            >
              <span
                className={cn(
                  'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                  showToastNotifications ? 'translate-x-6' : 'translate-x-1'
                )}
              />
            </button>
          </div>

          {/* Browser Notifications */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Browser Notifications</h3>
              <p className="text-sm text-gray-600">Show native browser notifications</p>
              {!showBrowserNotifications && 'Notification' in window && Notification.permission === 'default' && (
                <p className="text-xs text-orange-600 mt-1">Permission required</p>
              )}
            </div>
            <div className="flex items-center space-x-2">
              {!showBrowserNotifications && 'Notification' in window && Notification.permission === 'default' && (
                <Button
                  onClick={handleRequestBrowserPermission}
                  size="sm"
                  variant="outline"
                >
                  Grant Permission
                </Button>
              )}
              <button
                onClick={() => setShowBrowserNotifications(!showBrowserNotifications)}
                disabled={'Notification' in window && Notification.permission === 'denied'}
                className={cn(
                  'relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                  showBrowserNotifications ? 'bg-blue-600' : 'bg-gray-200',
                  'Notification' in window && Notification.permission === 'denied' && 'opacity-50 cursor-not-allowed'
                )}
                role="switch"
                aria-checked={showBrowserNotifications}
              >
                <span
                  className={cn(
                    'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                    showBrowserNotifications ? 'translate-x-6' : 'translate-x-1'
                  )}
                />
              </button>
            </div>
          </div>

          {/* Test Notifications */}
          <div className="pt-4 border-t border-gray-200">
            <Button
              onClick={testNotification}
              disabled={isTestingNotifications || (!showToastNotifications && !showBrowserNotifications)}
              className="w-full sm:w-auto"
            >
              {isTestingNotifications ? 'Sending Test...' : 'Send Test Notification'}
            </Button>
            <p className="text-sm text-gray-500 mt-2">
              This will send a test notification using your current preferences
            </p>
          </div>
        </div>
      </div>

      {/* Message Preferences */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-green-100 rounded-lg">
            <ChatBubbleLeftRightIcon className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Message Preferences</h2>
            <p className="text-gray-600">Configure real-time messaging features</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Real-time Messages</h3>
              <p className="text-sm text-gray-600">
                Status: {messagesConnected ? 'Connected' : 'Disconnected'}
              </p>
            </div>
            <RealtimeStatus size="md" variant="badge" showText />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Typing Indicators</h3>
              <p className="text-sm text-gray-600">Show when others are typing</p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">
                {typingUsers.length} user(s) typing
              </span>
              <CheckCircleIcon className="h-5 w-5 text-green-500" />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Online Status</h3>
              <p className="text-sm text-gray-600">Show online/offline status</p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">
                {onlineUsers.length} user(s) online
              </span>
              <CheckCircleIcon className="h-5 w-5 text-green-500" />
            </div>
          </div>
        </div>
      </div>

      {/* System Information */}
      <div className="bg-gray-50 rounded-xl border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">System Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700">Real-time Provider:</span>
            <span className="ml-2 text-gray-600">Supabase</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Connection Status:</span>
            <span className="ml-2 text-gray-600">{getConnectionStatus()}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Features Enabled:</span>
            <span className="ml-2 text-gray-600">{isRealtimeEnabled ? 'Yes' : 'No'}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Browser Support:</span>
            <span className="ml-2 text-gray-600">
              {'Notification' in window ? 'Yes' : 'No'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

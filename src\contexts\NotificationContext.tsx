/**
 * Real-time Notification Context
 * Provides global notification state and real-time updates
 */

'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRealtimeNotifications } from '@/hooks/useRealtimeNotifications';
import { RealtimeNotification } from '@/lib/supabase/client';
import { toast } from 'react-hot-toast';

interface NotificationContextType {
  // State
  unreadCount: number;
  recentNotifications: RealtimeNotification[];
  isConnected: boolean;
  isRealtimeEnabled: boolean;
  
  // Actions
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  clearRecentNotifications: () => void;
  requestBrowserPermission: () => Promise<boolean>;
  
  // Settings
  showToastNotifications: boolean;
  setShowToastNotifications: (show: boolean) => void;
  showBrowserNotifications: boolean;
  setShowBrowserNotifications: (show: boolean) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: React.ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const { data: session } = useSession();
  const [showToastNotifications, setShowToastNotifications] = useState(true);
  const [showBrowserNotifications, setShowBrowserNotifications] = useState(false);

  // Custom notification handler
  const handleNewNotification = useCallback((notification: RealtimeNotification) => {
    console.log('🔔 New notification received:', notification);

    // Show browser notification if enabled
    if (showBrowserNotifications && 'Notification' in window && Notification.permission === 'granted') {
      const notificationText = getNotificationText(notification);
      new Notification('HIFNF', {
        body: notificationText,
        icon: '/favicon.ico',
        tag: notification.id,
        requireInteraction: false,
      });
    }

    // Show toast notification if enabled
    if (showToastNotifications) {
      const notificationText = getNotificationText(notification);
      toast.success(notificationText, {
        duration: 4000,
        position: 'top-right',
        icon: getNotificationIcon(notification.type),
      });
    }
  }, [showBrowserNotifications, showToastNotifications]);

  // Use real-time notifications hook
  const {
    isConnected,
    unreadCount,
    recentNotifications,
    isRealtimeEnabled,
    markAsRead,
    markAllAsRead,
    clearRecentNotifications,
    requestNotificationPermission,
  } = useRealtimeNotifications({
    onNewNotification: handleNewNotification,
    showToast: false, // We handle toasts manually
    autoMarkAsRead: false,
  });

  // Load notification preferences from localStorage
  useEffect(() => {
    const savedToastPref = localStorage.getItem('showToastNotifications');
    const savedBrowserPref = localStorage.getItem('showBrowserNotifications');
    
    if (savedToastPref !== null) {
      setShowToastNotifications(JSON.parse(savedToastPref));
    }
    
    if (savedBrowserPref !== null) {
      setShowBrowserNotifications(JSON.parse(savedBrowserPref));
    }
  }, []);

  // Save notification preferences to localStorage
  useEffect(() => {
    localStorage.setItem('showToastNotifications', JSON.stringify(showToastNotifications));
  }, [showToastNotifications]);

  useEffect(() => {
    localStorage.setItem('showBrowserNotifications', JSON.stringify(showBrowserNotifications));
  }, [showBrowserNotifications]);

  // Request browser notification permission when enabled
  const requestBrowserPermission = useCallback(async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      const granted = permission === 'granted';
      setShowBrowserNotifications(granted);
      return granted;
    }
    return false;
  }, []);

  const contextValue: NotificationContextType = {
    // State
    unreadCount,
    recentNotifications,
    isConnected,
    isRealtimeEnabled,
    
    // Actions
    markAsRead,
    markAllAsRead,
    clearRecentNotifications,
    requestBrowserPermission,
    
    // Settings
    showToastNotifications,
    setShowToastNotifications,
    showBrowserNotifications,
    setShowBrowserNotifications,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
}

// Hook to use notification context
export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}

// Helper functions
function getNotificationText(notification: RealtimeNotification): string {
  const { type, data } = notification;
  
  switch (type) {
    case 'message':
      return 'You have a new message';
    case 'like':
      return 'Someone liked your post';
    case 'comment':
      return 'Someone commented on your post';
    case 'subscription':
      return 'You have a new subscriber';
    case 'group_invite':
      return 'You have been invited to a group';
    case 'event_invite':
      return 'You have been invited to an event';
    case 'fan_page_follow':
      return 'Someone followed your fan page';
    case 'friend_request':
      return 'You have a new friend request';
    case 'friend_accept':
      return 'Your friend request was accepted';
    default:
      return 'You have a new notification';
  }
}

function getNotificationIcon(type: string): string {
  switch (type) {
    case 'message':
      return '💬';
    case 'like':
      return '❤️';
    case 'comment':
      return '💭';
    case 'subscription':
      return '👥';
    case 'group_invite':
      return '👥';
    case 'event_invite':
      return '📅';
    case 'fan_page_follow':
      return '⭐';
    case 'friend_request':
      return '🤝';
    case 'friend_accept':
      return '✅';
    default:
      return '🔔';
  }
}

/**
 * Simple Supabase Connection Test
 */

import dotenv from 'dotenv';
import path from 'path';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env') });

async function testSupabaseConnection() {
  console.log('🔍 Testing Supabase Connection...\n');

  // Check environment variables
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  console.log('1️⃣ Environment Variables:');
  console.log('   URL:', url ? '✅ Found' : '❌ Missing');
  console.log('   Anon Key:', anonKey ? '✅ Found' : '❌ Missing');
  console.log('   Service Key:', serviceKey ? '✅ Found' : '❌ Missing');

  if (!url || !anonKey || !serviceKey) {
    console.error('\n❌ Missing required environment variables');
    return false;
  }

  // Test basic connection
  console.log('\n2️⃣ Testing Connection...');
  try {
    const supabase = createClient(url, serviceKey);
    
    // Simple test query
    const { data, error } = await supabase
      .from('_test_table_that_does_not_exist')
      .select('*')
      .limit(1);

    // We expect an error about table not existing, which means connection works
    if (error && error.message.includes('relation') && error.message.includes('does not exist')) {
      console.log('   ✅ Connection successful (expected table error)');
      return true;
    } else if (error) {
      console.error('   ❌ Unexpected error:', error.message);
      return false;
    } else {
      console.log('   ✅ Connection successful');
      return true;
    }
  } catch (error) {
    console.error('   ❌ Connection failed:', error);
    return false;
  }
}

// Run the test
if (require.main === module) {
  testSupabaseConnection()
    .then((success) => {
      console.log(success ? '\n✅ Test passed!' : '\n❌ Test failed!');
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Test error:', error);
      process.exit(1);
    });
}

/**
 * Real-time Notifications Hook
 * Handles real-time notification subscriptions using Supabase
 */

import { useEffect, useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { connectionManager, RealtimeNotification, isRealtimeEnabled } from '@/lib/supabase/client';
import { toast } from 'react-hot-toast';

interface UseRealtimeNotificationsProps {
  onNewNotification?: (notification: RealtimeNotification) => void;
  showToast?: boolean;
  autoMarkAsRead?: boolean;
}

interface RealtimeNotificationsState {
  isConnected: boolean;
  unreadCount: number;
  recentNotifications: RealtimeNotification[];
  connectionError: string | null;
}

export function useRealtimeNotifications({
  onNewNotification,
  showToast = true,
  autoMarkAsRead = false,
}: UseRealtimeNotificationsProps = {}) {
  const { data: session } = useSession();
  const [state, setState] = useState<RealtimeNotificationsState>({
    isConnected: false,
    unreadCount: 0,
    recentNotifications: [],
    connectionError: null,
  });

  // Generate notification display text
  const getNotificationText = useCallback((notification: RealtimeNotification): string => {
    const { type, data } = notification;
    
    switch (type) {
      case 'message':
        return 'You have a new message';
      case 'like':
        return 'Someone liked your post';
      case 'comment':
        return 'Someone commented on your post';
      case 'subscription':
        return 'You have a new subscriber';
      case 'group_invite':
        return 'You have been invited to a group';
      case 'event_invite':
        return 'You have been invited to an event';
      case 'fan_page_follow':
        return 'Someone followed your fan page';
      default:
        return 'You have a new notification';
    }
  }, []);

  // Handle new notification
  const handleNewNotification = useCallback((notification: RealtimeNotification) => {
    console.log('🔔 New real-time notification received:', notification);

    // Update state
    setState(prev => ({
      ...prev,
      unreadCount: prev.unreadCount + 1,
      recentNotifications: [notification, ...prev.recentNotifications.slice(0, 9)], // Keep last 10
    }));

    // Show toast notification
    if (showToast) {
      const notificationText = getNotificationText(notification);
      toast.success(notificationText, {
        duration: 4000,
        position: 'top-right',
        icon: '🔔',
      });
    }

    // Call custom handler
    onNewNotification?.(notification);

    // Auto mark as read if enabled
    if (autoMarkAsRead) {
      // Mark as read in MySQL via API
      fetch(`/api/notifications/${notification.mysql_id}`, {
        method: 'PATCH',
      }).catch(error => {
        console.error('Failed to auto-mark notification as read:', error);
      });
    }
  }, [onNewNotification, showToast, autoMarkAsRead, getNotificationText]);

  // Subscribe to real-time notifications
  useEffect(() => {
    if (!isRealtimeEnabled() || !session?.user?.id) {
      return;
    }

    const currentUserId = session.user.id;
    
    try {
      // Subscribe to notifications for current user
      const notificationChannel = connectionManager.subscribeToNotifications(
        currentUserId,
        handleNewNotification
      );

      setState(prev => ({ ...prev, isConnected: true, connectionError: null }));

      // Cleanup function
      return () => {
        connectionManager.unsubscribe(`notifications:${currentUserId}`);
      };
    } catch (error) {
      console.error('Failed to setup real-time notification subscriptions:', error);
      setState(prev => ({
        ...prev,
        isConnected: false,
        connectionError: String(error),
      }));
    }
  }, [session?.user?.id, handleNewNotification]);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'PATCH',
      });

      if (response.ok) {
        setState(prev => ({
          ...prev,
          unreadCount: Math.max(0, prev.unreadCount - 1),
          recentNotifications: prev.recentNotifications.map(n =>
            n.mysql_id === notificationId ? { ...n, read_status: true } : n
          ),
        }));
      }
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'PATCH',
      });

      if (response.ok) {
        setState(prev => ({
          ...prev,
          unreadCount: 0,
          recentNotifications: prev.recentNotifications.map(n => ({
            ...n,
            read_status: true,
          })),
        }));
      }
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  }, []);

  // Clear recent notifications
  const clearRecentNotifications = useCallback(() => {
    setState(prev => ({
      ...prev,
      recentNotifications: [],
    }));
  }, []);

  // Get connection status
  const getConnectionStatus = useCallback(() => {
    return connectionManager.getConnectionStatus();
  }, []);

  // Request notification permission (for browser notifications)
  const requestNotificationPermission = useCallback(async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }, []);

  // Show browser notification
  const showBrowserNotification = useCallback((notification: RealtimeNotification) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      const notificationText = getNotificationText(notification);
      new Notification('HIFNF', {
        body: notificationText,
        icon: '/favicon.ico',
        tag: notification.id,
      });
    }
  }, [getNotificationText]);

  return {
    // State
    isConnected: state.isConnected && isRealtimeEnabled(),
    unreadCount: state.unreadCount,
    recentNotifications: state.recentNotifications,
    connectionError: state.connectionError,
    isRealtimeEnabled: isRealtimeEnabled(),

    // Actions
    markAsRead,
    markAllAsRead,
    clearRecentNotifications,
    getConnectionStatus,
    requestNotificationPermission,
    showBrowserNotification,
  };
}

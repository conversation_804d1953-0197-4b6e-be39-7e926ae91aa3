/**
 * Test Real-time Messaging System
 * Tests the complete real-time messaging flow
 */

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables first
dotenv.config({ path: path.join(process.cwd(), '.env') });

// Verify environment variables are loaded
console.log('Environment check:');
console.log('SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Found' : 'Missing');
console.log('ENABLE_REALTIME:', process.env.ENABLE_REALTIME);

import { supabaseClient, connectionManager, isRealtimeEnabled } from '../lib/supabase/client';
import { syncMessage, syncNotification, updatePresence } from '../lib/supabase/syncService';
import { v4 as uuidv4 } from 'uuid';

async function testRealtimeMessaging() {
  console.log('💬 Testing Real-time Messaging System...\n');

  if (!isRealtimeEnabled()) {
    console.log('⚠️  Real-time is disabled. Enable ENABLE_REALTIME=true in .env');
    return false;
  }

  try {
    // Test 1: Connection Status
    console.log('1️⃣ Testing Connection Status...');
    const connectionStatus = connectionManager.getConnectionStatus();
    console.log('   Connection Status:', connectionStatus);

    // Test 2: User Presence
    console.log('\n2️⃣ Testing User Presence...');
    const testUserId = 'test-user-realtime-' + Date.now();
    
    const presenceResult = await updatePresence(testUserId, 'online');
    console.log('   Presence Update:', presenceResult.success ? '✅ Success' : '❌ Failed');
    if (!presenceResult.success) {
      console.error('   Error:', presenceResult.error);
    }

    // Test 3: Message Subscription
    console.log('\n3️⃣ Testing Message Subscription...');
    let messageReceived = false;
    
    const messageChannel = connectionManager.subscribeToMessages(
      testUserId,
      (message) => {
        console.log('   📨 Message received:', message.content);
        messageReceived = true;
      }
    );

    // Test 4: Send Test Message
    console.log('\n4️⃣ Testing Message Sending...');
    const testMessage = {
      id: uuidv4(),
      senderId: 'test-sender-' + Date.now(),
      receiverId: testUserId,
      content: 'Test real-time message: ' + new Date().toISOString(),
      createdAt: new Date(),
    };

    const messageResult = await syncMessage(testMessage);
    console.log('   Message Sync:', messageResult.success ? '✅ Success' : '❌ Failed');
    if (!messageResult.success) {
      console.error('   Error:', messageResult.error);
    }

    // Wait for real-time message
    console.log('   Waiting for real-time delivery...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('   Real-time Delivery:', messageReceived ? '✅ Success' : '⚠️  Not received');

    // Test 5: Notification Subscription
    console.log('\n5️⃣ Testing Notification Subscription...');
    let notificationReceived = false;

    const notificationChannel = connectionManager.subscribeToNotifications(
      testUserId,
      (notification) => {
        console.log('   🔔 Notification received:', notification.type);
        notificationReceived = true;
      }
    );

    // Test 6: Send Test Notification
    console.log('\n6️⃣ Testing Notification Sending...');
    const testNotification = {
      id: uuidv4(),
      recipientId: testUserId,
      type: 'test_realtime_notification',
      senderId: 'test-sender-' + Date.now(),
      data: {
        message: 'Test real-time notification',
        timestamp: new Date().toISOString(),
      },
      createdAt: new Date(),
    };

    const notificationResult = await syncNotification(testNotification);
    console.log('   Notification Sync:', notificationResult.success ? '✅ Success' : '❌ Failed');
    if (!notificationResult.success) {
      console.error('   Error:', notificationResult.error);
    }

    // Wait for real-time notification
    console.log('   Waiting for real-time delivery...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('   Real-time Delivery:', notificationReceived ? '✅ Success' : '⚠️  Not received');

    // Test 7: Typing Indicator
    console.log('\n7️⃣ Testing Typing Indicator...');
    const typingResult = await updatePresence(testUserId, 'online', 'test-conversation-123');
    console.log('   Typing Indicator:', typingResult.success ? '✅ Success' : '❌ Failed');

    // Clear typing
    await updatePresence(testUserId, 'online');
    console.log('   Clear Typing:', '✅ Success');

    // Test 8: Presence Subscription
    console.log('\n8️⃣ Testing Presence Subscription...');
    let presenceReceived = false;

    const presenceChannel = connectionManager.subscribeToPresence(
      testUserId,
      (presenceList) => {
        console.log('   👥 Presence update received, users:', presenceList.length);
        presenceReceived = true;
      }
    );

    // Update presence to trigger subscription
    await updatePresence(testUserId, 'away');
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log('   Presence Subscription:', presenceReceived ? '✅ Success' : '⚠️  Not received');

    // Cleanup
    console.log('\n9️⃣ Cleanup...');
    connectionManager.unsubscribe(`messages:${testUserId}`);
    connectionManager.unsubscribe(`notifications:${testUserId}`);
    connectionManager.unsubscribe('presence:global');
    await updatePresence(testUserId, 'offline');
    console.log('   Cleanup completed ✅');

    // Summary
    const allTestsPassed = messageResult.success && notificationResult.success && typingResult.success;
    console.log('\n📋 Test Summary:');
    console.log('   Connection:', connectionStatus === 'connected' ? '✅' : '❌');
    console.log('   Message Sync:', messageResult.success ? '✅' : '❌');
    console.log('   Message Real-time:', messageReceived ? '✅' : '⚠️');
    console.log('   Notification Sync:', notificationResult.success ? '✅' : '❌');
    console.log('   Notification Real-time:', notificationReceived ? '✅' : '⚠️');
    console.log('   Typing Indicator:', typingResult.success ? '✅' : '❌');
    console.log('   Presence Updates:', presenceReceived ? '✅' : '⚠️');
    console.log('   Overall:', allTestsPassed ? '✅ All core tests passed' : '⚠️  Some tests had issues');

    return allTestsPassed;
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
}

// Performance test for real-time
async function testRealtimePerformance() {
  console.log('\n⚡ Testing Real-time Performance...');
  
  if (!isRealtimeEnabled()) {
    console.log('⚠️  Real-time is disabled. Skipping performance test.');
    return true;
  }

  const startTime = Date.now();
  const testUserId = 'perf-test-user-' + Date.now();
  const messagePromises = [];

  // Send 5 messages concurrently
  for (let i = 0; i < 5; i++) {
    const messagePromise = syncMessage({
      id: uuidv4(),
      senderId: `perf-sender-${i}`,
      receiverId: testUserId,
      content: `Performance test message ${i}`,
      createdAt: new Date(),
    });
    messagePromises.push(messagePromise);
  }

  try {
    const results = await Promise.all(messagePromises);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const successCount = results.filter(r => r.success).length;
    
    console.log(`   📊 Performance Results:`);
    console.log(`      Messages sent: 5`);
    console.log(`      Successful: ${successCount}`);
    console.log(`      Failed: ${5 - successCount}`);
    console.log(`      Duration: ${duration}ms`);
    console.log(`      Average per message: ${(duration / 5).toFixed(2)}ms`);
    
    return successCount === 5;
  } catch (error) {
    console.error('   ❌ Performance test failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Real-time Messaging Tests...\n');
  
  const basicTestsPass = await testRealtimeMessaging();
  const performanceTestsPass = await testRealtimePerformance();
  
  const allTestsPass = basicTestsPass && performanceTestsPass;
  
  console.log('\n🏁 Final Results:');
  console.log('   Basic Tests:', basicTestsPass ? '✅ Passed' : '❌ Failed');
  console.log('   Performance Tests:', performanceTestsPass ? '✅ Passed' : '❌ Failed');
  console.log('   Overall:', allTestsPass ? '✅ All tests passed' : '❌ Some tests failed');
  
  return allTestsPass;
}

// Run the tests
if (require.main === module) {
  runAllTests()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    });
}

export { testRealtimeMessaging, testRealtimePerformance, runAllTests };

/**
 * Test Supabase Connection and Configuration
 * Run this script to validate Supabase setup before proceeding
 */

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env') });

import { supabaseClient, supabaseAdmin, getSupabaseStatus } from '../lib/supabase/client';
import { supabaseConfig } from '../lib/supabase/config';
import { syncService } from '../lib/supabase/syncService';

async function testSupabaseConnection() {
  console.log('🔍 Testing Supabase Connection and Configuration...\n');

  // Test 1: Configuration validation
  console.log('1️⃣ Validating Configuration...');
  try {
    console.log('   ✅ Supabase URL:', supabaseConfig.url);
    console.log('   ✅ Anon Key:', supabaseConfig.anonKey ? '***configured***' : '❌ Missing');
    console.log('   ✅ Service Role Key:', supabaseConfig.serviceRoleKey ? '***configured***' : '❌ Missing');
    console.log('   ✅ Real-time Enabled:', supabaseConfig.realtime.enabled);
    console.log('   ✅ Provider:', supabaseConfig.realtime.provider);
  } catch (error) {
    console.error('   ❌ Configuration Error:', error);
    return false;
  }

  // Test 2: Basic connection test
  console.log('\n2️⃣ Testing Basic Connection...');
  try {
    const { data, error } = await supabaseAdmin.from('_test').select('*').limit(1);
    if (error && !error.message.includes('relation "_test" does not exist')) {
      console.error('   ❌ Connection Error:', error.message);
      return false;
    }
    console.log('   ✅ Supabase connection successful');
  } catch (error) {
    console.error('   ❌ Connection failed:', error);
    return false;
  }

  // Test 3: Check if tables exist (they might not exist yet)
  console.log('\n3️⃣ Checking Table Existence...');
  try {
    const tables = [
      supabaseConfig.tables.messages,
      supabaseConfig.tables.notifications,
      supabaseConfig.tables.presence,
    ];

    for (const table of tables) {
      try {
        const { error } = await supabaseAdmin.from(table).select('*').limit(1);
        if (error) {
          console.log(`   ⚠️  Table '${table}' does not exist yet (will be created)`);
        } else {
          console.log(`   ✅ Table '${table}' exists`);
        }
      } catch (err) {
        console.log(`   ⚠️  Table '${table}' not accessible (will be created)`);
      }
    }
  } catch (error) {
    console.error('   ❌ Table check failed:', error);
  }

  // Test 4: Real-time connection test
  console.log('\n4️⃣ Testing Real-time Connection...');
  try {
    const channel = supabaseClient.channel('test-channel');
    
    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Real-time connection timeout'));
      }, 10000);

      channel.subscribe((status) => {
        clearTimeout(timeout);
        if (status === 'SUBSCRIBED') {
          console.log('   ✅ Real-time connection successful');
          resolve(true);
        } else if (status === 'CHANNEL_ERROR') {
          reject(new Error('Real-time channel error'));
        }
      });
    });

    // Cleanup
    supabaseClient.removeChannel(channel);
  } catch (error) {
    console.error('   ❌ Real-time connection failed:', error);
    return false;
  }

  // Test 5: Sync service health check
  console.log('\n5️⃣ Testing Sync Service...');
  try {
    const health = await syncService.healthCheck();
    if (health.healthy) {
      console.log('   ✅ Sync service healthy');
      console.log('   📊 Details:', health.details);
    } else {
      console.log('   ⚠️  Sync service issues:', health.details);
    }
  } catch (error) {
    console.error('   ❌ Sync service test failed:', error);
  }

  // Test 6: Get status summary
  console.log('\n6️⃣ System Status Summary...');
  try {
    const status = getSupabaseStatus();
    console.log('   📊 Status:', status);
  } catch (error) {
    console.error('   ❌ Status check failed:', error);
  }

  console.log('\n✅ Supabase connection test completed!');
  console.log('\n📝 Next Steps:');
  console.log('   1. If tables don\'t exist, run the schema.sql in Supabase dashboard');
  console.log('   2. Configure Row Level Security policies');
  console.log('   3. Test real-time messaging functionality');
  
  return true;
}

// Run the test
if (require.main === module) {
  testSupabaseConnection()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export { testSupabaseConnection };

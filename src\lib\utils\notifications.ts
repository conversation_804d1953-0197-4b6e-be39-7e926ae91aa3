import { db } from "@/lib/db";
import { notifications } from "@/lib/db/schema";
import { syncNotification } from "@/lib/supabase/syncService";
import { REALTIME_ENABLED } from "@/lib/supabase/config";
import { v4 as uuidv4 } from "uuid";

interface NotificationData {
  id: string;
  type: string;
  recipientId: string;
  senderId: string;
  read: boolean;
  createdAt: string;
  sender?: {
    id: string;
    name: string;
    image: string;
  };
  postId?: string;
  commentId?: string;
  messageId?: string;
  friendshipId?: string;
  fanPageId?: string;
  fanPagePostId?: string;
  groupId?: string;
  eventId?: string;
  storeId?: string;
  productId?: string;
}

interface CreateNotificationParams {
  recipientId: string;
  type: string;
  senderId?: string;
  postId?: string;
  commentId?: string;
  messageId?: string;
  friendshipId?: string;
  fanPageId?: string;
  fanPagePostId?: string;
  groupId?: string;
  eventId?: string;
  storeId?: string;
  productId?: string;
  subscriptionId?: string;
  data?: Record<string, any>;
}

/**
 * Create a notification in MySQL and sync to Supabase for real-time
 */
export async function createNotification(params: CreateNotificationParams): Promise<string> {
  const notificationId = uuidv4();

  try {
    // Insert into MySQL first (source of truth)
    await db.insert(notifications).values({
      id: notificationId,
      recipientId: params.recipientId,
      type: params.type as any, // Type assertion for enum
      senderId: params.senderId,
      postId: params.postId,
      commentId: params.commentId,
      messageId: params.messageId,
      friendshipId: params.friendshipId,
      fanPageId: params.fanPageId,
      fanPagePostId: params.fanPagePostId,
      groupId: params.groupId,
      eventId: params.eventId,
      storeId: params.storeId,
      productId: params.productId,
      subscriptionId: params.subscriptionId,
      read: false,
    });

    // Sync to Supabase for real-time (async, non-blocking)
    if (REALTIME_ENABLED) {
      syncNotification({
        id: notificationId,
        recipientId: params.recipientId,
        type: params.type,
        senderId: params.senderId,
        data: {
          postId: params.postId,
          commentId: params.commentId,
          messageId: params.messageId,
          friendshipId: params.friendshipId,
          fanPageId: params.fanPageId,
          fanPagePostId: params.fanPagePostId,
          groupId: params.groupId,
          eventId: params.eventId,
          storeId: params.storeId,
          productId: params.productId,
          subscriptionId: params.subscriptionId,
          ...params.data,
        },
        createdAt: new Date(),
      }).catch(error => {
        console.error('Failed to sync notification to Supabase:', error);
        // Log error but don't fail the request
      });
    }

    console.log(`✅ Notification created: ${params.type} for user ${params.recipientId}`);
    return notificationId;
  } catch (error) {
    console.error('Failed to create notification:', error);
    throw error;
  }
}

// Legacy function for backward compatibility
export function emitNotification(notification: NotificationData) {
  console.log('📡 Notification created (using legacy function):', notification.type);
  // This function is now deprecated, use createNotification instead
}

// Helper function to create notification data
export function createNotificationData(
  type: string,
  recipientId: string,
  senderId: string,
  options: {
    postId?: string;
    commentId?: string;
    messageId?: string;
    friendshipId?: string;
    fanPageId?: string;
    fanPagePostId?: string;
    groupId?: string;
    eventId?: string;
    storeId?: string;
    productId?: string;
    sender?: {
      id: string;
      name: string;
      image: string;
    };
  } = {}
): NotificationData {
  return {
    id: '', // Will be set by the database
    type,
    recipientId,
    senderId,
    read: false,
    createdAt: new Date().toISOString(),
    ...options,
  };
}

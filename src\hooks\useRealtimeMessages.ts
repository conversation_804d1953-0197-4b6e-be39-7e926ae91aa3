/**
 * Real-time Messages Hook
 * Handles real-time message subscriptions using Supabase
 */

import { useEffect, useState, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { connectionManager, RealtimeMessage, isRealtimeEnabled } from '@/lib/supabase/client';
import { updatePresence } from '@/lib/supabase/syncService';

interface UseRealtimeMessagesProps {
  userId?: string;
  onNewMessage?: (message: RealtimeMessage) => void;
  onTypingChange?: (typingUsers: string[]) => void;
  onPresenceChange?: (onlineUsers: string[]) => void;
}

interface RealtimeMessagesState {
  isConnected: boolean;
  isTyping: boolean;
  typingUsers: string[];
  onlineUsers: string[];
  connectionError: string | null;
}

export function useRealtimeMessages({
  userId,
  onNewMessage,
  onTypingChange,
  onPresenceChange,
}: UseRealtimeMessagesProps = {}) {
  const { data: session } = useSession();
  const [state, setState] = useState<RealtimeMessagesState>({
    isConnected: false,
    isTyping: false,
    typingUsers: [],
    onlineUsers: [],
    connectionError: null,
  });

  const typingTimeoutRef = useRef<NodeJS.Timeout>();
  const currentUserIdRef = useRef<string>();

  // Update current user ID
  useEffect(() => {
    if (session?.user?.id) {
      currentUserIdRef.current = session.user.id;
    }
  }, [session?.user?.id]);

  // Subscribe to real-time messages
  useEffect(() => {
    if (!isRealtimeEnabled() || !session?.user?.id) {
      return;
    }

    const currentUserId = session.user.id;
    
    try {
      // Subscribe to messages for current user
      const messageChannel = connectionManager.subscribeToMessages(
        currentUserId,
        (message: RealtimeMessage) => {
          console.log('📨 New real-time message received:', message);
          onNewMessage?.(message);
        }
      );

      // Subscribe to presence updates
      const presenceChannel = connectionManager.subscribeToPresence(
        currentUserId,
        (presenceList) => {
          const onlineUserIds = presenceList
            .filter(p => p.status === 'online')
            .map(p => p.user_id);
          
          const typingUserIds = presenceList
            .filter(p => p.typing_in && p.user_id !== currentUserId)
            .map(p => p.user_id);

          setState(prev => ({
            ...prev,
            onlineUsers: onlineUserIds,
            typingUsers: typingUserIds,
            isConnected: true,
            connectionError: null,
          }));

          onPresenceChange?.(onlineUserIds);
          onTypingChange?.(typingUserIds);
        }
      );

      // Update user presence to online
      updatePresence(currentUserId, 'online').catch(error => {
        console.error('Failed to update presence:', error);
      });

      setState(prev => ({ ...prev, isConnected: true, connectionError: null }));

      // Cleanup function
      return () => {
        connectionManager.unsubscribe(`messages:${currentUserId}`);
        connectionManager.unsubscribe('presence:global');
        
        // Set user offline when component unmounts
        updatePresence(currentUserId, 'offline').catch(error => {
          console.error('Failed to update offline presence:', error);
        });
      };
    } catch (error) {
      console.error('Failed to setup real-time subscriptions:', error);
      setState(prev => ({
        ...prev,
        isConnected: false,
        connectionError: String(error),
      }));
    }
  }, [session?.user?.id, onNewMessage, onPresenceChange, onTypingChange]);

  // Start typing indicator
  const startTyping = useCallback((conversationId?: string) => {
    if (!isRealtimeEnabled() || !currentUserIdRef.current) {
      return;
    }

    setState(prev => ({ ...prev, isTyping: true }));

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Update presence with typing status
    updatePresence(currentUserIdRef.current, 'online', conversationId).catch(error => {
      console.error('Failed to update typing presence:', error);
    });

    // Auto-stop typing after 3 seconds
    typingTimeoutRef.current = setTimeout(() => {
      stopTyping();
    }, 3000);
  }, []);

  // Stop typing indicator
  const stopTyping = useCallback(() => {
    if (!isRealtimeEnabled() || !currentUserIdRef.current) {
      return;
    }

    setState(prev => ({ ...prev, isTyping: false }));

    // Clear timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = undefined;
    }

    // Update presence without typing status
    updatePresence(currentUserIdRef.current, 'online').catch(error => {
      console.error('Failed to clear typing presence:', error);
    });
  }, []);

  // Mark user as away
  const setAway = useCallback(() => {
    if (!isRealtimeEnabled() || !currentUserIdRef.current) {
      return;
    }

    updatePresence(currentUserIdRef.current, 'away').catch(error => {
      console.error('Failed to update away presence:', error);
    });
  }, []);

  // Mark user as online
  const setOnline = useCallback(() => {
    if (!isRealtimeEnabled() || !currentUserIdRef.current) {
      return;
    }

    updatePresence(currentUserIdRef.current, 'online').catch(error => {
      console.error('Failed to update online presence:', error);
    });
  }, []);

  // Get connection status
  const getConnectionStatus = useCallback(() => {
    return connectionManager.getConnectionStatus();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  return {
    // State
    isConnected: state.isConnected && isRealtimeEnabled(),
    isTyping: state.isTyping,
    typingUsers: state.typingUsers,
    onlineUsers: state.onlineUsers,
    connectionError: state.connectionError,
    isRealtimeEnabled: isRealtimeEnabled(),

    // Actions
    startTyping,
    stopTyping,
    setAway,
    setOnline,
    getConnectionStatus,
  };
}

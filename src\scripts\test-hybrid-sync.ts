/**
 * Test Hybrid Sync Service
 * Tests the sync between MySQL and Supabase
 */

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env') });

import { syncService, syncMessage, syncNotification } from '../lib/supabase/syncService';
import { v4 as uuidv4 } from 'uuid';

async function testHybridSync() {
  console.log('🔄 Testing Hybrid Sync Service...\n');

  try {
    // Test 1: Health Check
    console.log('1️⃣ Testing Health Check...');
    const health = await syncService.healthCheck();
    console.log('   Health Status:', health.healthy ? '✅ Healthy' : '❌ Unhealthy');
    console.log('   Details:', health.details);

    if (!health.healthy) {
      console.error('❌ Sync service is not healthy. Stopping tests.');
      return false;
    }

    // Test 2: Sync Test Message
    console.log('\n2️⃣ Testing Message Sync...');
    const testMessageId = uuidv4();
    const testMessage = {
      id: testMessageId,
      senderId: 'test-sender-123',
      receiverId: 'test-receiver-456',
      content: 'Test message for hybrid sync',
      createdAt: new Date(),
    };

    const messageResult = await syncMessage(testMessage);
    console.log('   Message Sync Result:', messageResult.success ? '✅ Success' : '❌ Failed');
    if (!messageResult.success) {
      console.error('   Error:', messageResult.error);
    }

    // Test 3: Sync Test Notification
    console.log('\n3️⃣ Testing Notification Sync...');
    const testNotificationId = uuidv4();
    const testNotification = {
      id: testNotificationId,
      recipientId: 'test-recipient-789',
      type: 'test_notification',
      senderId: 'test-sender-123',
      data: {
        messageId: testMessageId,
        testData: 'This is a test notification',
      },
      createdAt: new Date(),
    };

    const notificationResult = await syncNotification(testNotification);
    console.log('   Notification Sync Result:', notificationResult.success ? '✅ Success' : '❌ Failed');
    if (!notificationResult.success) {
      console.error('   Error:', notificationResult.error);
    }

    // Test 4: Get Sync Statistics
    console.log('\n4️⃣ Testing Sync Statistics...');
    const stats = await syncService.getSyncStats();
    if (stats) {
      console.log('   📊 Sync Statistics:');
      console.log('      Messages in Supabase:', stats.messages);
      console.log('      Notifications in Supabase:', stats.notifications);
      console.log('      Active Users:', stats.activeUsers);
      console.log('      Last Sync:', stats.lastSync);
    } else {
      console.log('   ⚠️  Could not retrieve sync statistics');
    }

    // Test 5: Test User Presence Update
    console.log('\n5️⃣ Testing User Presence...');
    const { updatePresence } = await import('../lib/supabase/syncService');
    const presenceResult = await updatePresence('test-user-presence', 'online');
    console.log('   Presence Update Result:', presenceResult.success ? '✅ Success' : '❌ Failed');
    if (!presenceResult.success) {
      console.error('   Error:', presenceResult.error);
    }

    // Test 6: Test Cleanup (optional)
    console.log('\n6️⃣ Testing Cleanup...');
    const cleanupResult = await syncService.cleanupOldData();
    console.log('   Cleanup Result:', cleanupResult.success ? '✅ Success' : '❌ Failed');
    if (!cleanupResult.success) {
      console.error('   Error:', cleanupResult.error);
    }

    console.log('\n✅ Hybrid Sync Service tests completed!');
    
    // Summary
    const allTestsPassed = messageResult.success && notificationResult.success && presenceResult.success;
    console.log('\n📋 Test Summary:');
    console.log('   Overall Status:', allTestsPassed ? '✅ All tests passed' : '⚠️  Some tests failed');
    console.log('   Message Sync:', messageResult.success ? '✅' : '❌');
    console.log('   Notification Sync:', notificationResult.success ? '✅' : '❌');
    console.log('   Presence Update:', presenceResult.success ? '✅' : '❌');
    console.log('   Cleanup:', cleanupResult.success ? '✅' : '❌');

    return allTestsPassed;
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
}

// Performance test
async function testSyncPerformance() {
  console.log('\n⚡ Testing Sync Performance...');
  
  const startTime = Date.now();
  const testPromises = [];

  // Create 10 concurrent sync operations
  for (let i = 0; i < 10; i++) {
    const messagePromise = syncMessage({
      id: uuidv4(),
      senderId: `perf-sender-${i}`,
      receiverId: `perf-receiver-${i}`,
      content: `Performance test message ${i}`,
      createdAt: new Date(),
    });

    const notificationPromise = syncNotification({
      id: uuidv4(),
      recipientId: `perf-recipient-${i}`,
      type: 'performance_test',
      data: { testIndex: i },
      createdAt: new Date(),
    });

    testPromises.push(messagePromise, notificationPromise);
  }

  try {
    const results = await Promise.all(testPromises);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const successCount = results.filter(r => r.success).length;
    const totalOperations = results.length;

    console.log(`   📊 Performance Results:`);
    console.log(`      Total Operations: ${totalOperations}`);
    console.log(`      Successful: ${successCount}`);
    console.log(`      Failed: ${totalOperations - successCount}`);
    console.log(`      Duration: ${duration}ms`);
    console.log(`      Average per operation: ${(duration / totalOperations).toFixed(2)}ms`);
    
    return successCount === totalOperations;
  } catch (error) {
    console.error('   ❌ Performance test failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Hybrid Sync Tests...\n');
  
  const basicTestsPass = await testHybridSync();
  const performanceTestsPass = await testSyncPerformance();
  
  const allTestsPass = basicTestsPass && performanceTestsPass;
  
  console.log('\n🏁 Final Results:');
  console.log('   Basic Tests:', basicTestsPass ? '✅ Passed' : '❌ Failed');
  console.log('   Performance Tests:', performanceTestsPass ? '✅ Passed' : '❌ Failed');
  console.log('   Overall:', allTestsPass ? '✅ All tests passed' : '❌ Some tests failed');
  
  return allTestsPass;
}

// Run the tests
if (require.main === module) {
  runAllTests()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    });
}

export { testHybridSync, testSyncPerformance, runAllTests };

/**
 * Hybrid Data Sync Service
 * Syncs data between MySQL (primary) and Supabase (real-time)
 * MySQL is the source of truth, Supabase is for real-time events only
 */

import { db } from '../db';
import { messages, notifications } from '../db/schema';
import { supabaseAdmin } from './client';
import { supabaseConfig } from './config';
import { eq, and, gte } from 'drizzle-orm';

export interface SyncResult {
  success: boolean;
  error?: string;
  synced?: number;
}

export interface MessageSyncData {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  createdAt: Date;
}

export interface NotificationSyncData {
  id: string;
  recipientId: string;
  type: string;
  senderId?: string;
  data: Record<string, any>;
  createdAt: Date;
}

class HybridSyncService {
  private syncInProgress = false;
  private lastSyncTime: Date = new Date(0);

  /**
   * Sync a new message to Supabase for real-time delivery
   */
  async syncMessageToSupabase(messageData: MessageSyncData): Promise<SyncResult> {
    try {
      const { error } = await supabaseAdmin
        .from(supabaseConfig.tables.messages)
        .insert({
          sender_id: messageData.senderId,
          receiver_id: messageData.receiverId,
          content: messageData.content,
          message_type: 'text',
          mysql_id: messageData.id,
          created_at: messageData.createdAt.toISOString(),
        });

      if (error) {
        console.error('Error syncing message to Supabase:', error);
        return { success: false, error: error.message };
      }

      console.log(`✅ Message ${messageData.id} synced to Supabase for real-time delivery`);
      return { success: true };
    } catch (error) {
      console.error('Error in syncMessageToSupabase:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * Sync a new notification to Supabase for real-time delivery
   */
  async syncNotificationToSupabase(notificationData: NotificationSyncData): Promise<SyncResult> {
    try {
      const { error } = await supabaseAdmin
        .from(supabaseConfig.tables.notifications)
        .insert({
          recipient_id: notificationData.recipientId,
          type: notificationData.type,
          data: {
            senderId: notificationData.senderId,
            ...notificationData.data,
          },
          mysql_id: notificationData.id,
          created_at: notificationData.createdAt.toISOString(),
          read_status: false,
        });

      if (error) {
        console.error('Error syncing notification to Supabase:', error);
        return { success: false, error: error.message };
      }

      console.log(`✅ Notification ${notificationData.id} synced to Supabase for real-time delivery`);
      return { success: true };
    } catch (error) {
      console.error('Error in syncNotificationToSupabase:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * Mark notification as read in Supabase
   */
  async markNotificationAsRead(mysqlNotificationId: string): Promise<SyncResult> {
    try {
      const { error } = await supabaseAdmin
        .from(supabaseConfig.tables.notifications)
        .update({ read_status: true })
        .eq('mysql_id', mysqlNotificationId);

      if (error) {
        console.error('Error marking notification as read in Supabase:', error);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error in markNotificationAsRead:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * Update user presence in Supabase
   */
  async updateUserPresence(userId: string, status: 'online' | 'offline' | 'away', typingIn?: string): Promise<SyncResult> {
    try {
      const { error } = await supabaseAdmin
        .from(supabaseConfig.tables.presence)
        .upsert({
          user_id: userId,
          status,
          last_seen: new Date().toISOString(),
          typing_in: typingIn || null,
        });

      if (error) {
        console.error('Error updating user presence:', error);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error in updateUserPresence:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * Cleanup old data from Supabase (keep it lightweight)
   */
  async cleanupOldData(): Promise<SyncResult> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Delete old messages
      const { error: messagesError } = await supabaseAdmin
        .from(supabaseConfig.tables.messages)
        .delete()
        .lt('created_at', thirtyDaysAgo.toISOString());

      if (messagesError) {
        console.error('Error cleaning up old messages:', messagesError);
      }

      // Delete old read notifications
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const { error: notificationsError } = await supabaseAdmin
        .from(supabaseConfig.tables.notifications)
        .delete()
        .lt('created_at', sevenDaysAgo.toISOString())
        .eq('read_status', true);

      if (notificationsError) {
        console.error('Error cleaning up old notifications:', notificationsError);
      }

      console.log('✅ Supabase cleanup completed');
      return { success: true };
    } catch (error) {
      console.error('Error in cleanupOldData:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * Get sync statistics
   */
  async getSyncStats() {
    try {
      const [messagesCount, notificationsCount, presenceCount] = await Promise.all([
        supabaseAdmin.from(supabaseConfig.tables.messages).select('id', { count: 'exact', head: true }),
        supabaseAdmin.from(supabaseConfig.tables.notifications).select('id', { count: 'exact', head: true }),
        supabaseAdmin.from(supabaseConfig.tables.presence).select('user_id', { count: 'exact', head: true }),
      ]);

      return {
        messages: messagesCount.count || 0,
        notifications: notificationsCount.count || 0,
        activeUsers: presenceCount.count || 0,
        lastSync: this.lastSyncTime,
      };
    } catch (error) {
      console.error('Error getting sync stats:', error);
      return null;
    }
  }

  /**
   * Health check for the sync service
   */
  async healthCheck(): Promise<{ healthy: boolean; details: any }> {
    try {
      // Test Supabase connection
      const { data, error } = await supabaseAdmin
        .from(supabaseConfig.tables.messages)
        .select('id')
        .limit(1);

      if (error) {
        return {
          healthy: false,
          details: { error: error.message, service: 'supabase' }
        };
      }

      // Test MySQL connection
      const mysqlTest = await db.select().from(messages).limit(1);

      return {
        healthy: true,
        details: {
          supabase: 'connected',
          mysql: 'connected',
          syncInProgress: this.syncInProgress,
          lastSync: this.lastSyncTime,
        }
      };
    } catch (error) {
      return {
        healthy: false,
        details: { error: String(error), service: 'mysql' }
      };
    }
  }
}

// Export singleton instance
export const syncService = new HybridSyncService();

// Helper functions for easy access
export const syncMessage = (messageData: MessageSyncData) => syncService.syncMessageToSupabase(messageData);
export const syncNotification = (notificationData: NotificationSyncData) => syncService.syncNotificationToSupabase(notificationData);
export const updatePresence = (userId: string, status: 'online' | 'offline' | 'away', typingIn?: string) => 
  syncService.updateUserPresence(userId, status, typingIn);
export const markNotificationRead = (mysqlNotificationId: string) => syncService.markNotificationAsRead(mysqlNotificationId);

/**
 * Supabase Error Handler
 * Comprehensive error handling and recovery for real-time features
 */

import { toast } from 'react-hot-toast';

export interface ErrorContext {
  operation: string;
  userId?: string;
  data?: any;
  timestamp: Date;
  retryCount?: number;
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

export class SupabaseErrorHandler {
  private static instance: SupabaseErrorHandler;
  private retryConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
  };

  private errorLog: ErrorContext[] = [];
  private maxLogSize = 100;

  static getInstance(): SupabaseErrorHandler {
    if (!SupabaseErrorHandler.instance) {
      SupabaseErrorHandler.instance = new SupabaseErrorHandler();
    }
    return SupabaseErrorHandler.instance;
  }

  /**
   * Handle and categorize errors
   */
  handleError(error: any, context: ErrorContext): void {
    const errorInfo = this.categorizeError(error);
    
    // Log error
    this.logError(error, context);
    
    // Show user-friendly message
    this.showUserMessage(errorInfo, context);
    
    // Report to monitoring (if configured)
    this.reportError(error, context);
  }

  /**
   * Categorize error types
   */
  private categorizeError(error: any): { type: string; severity: 'low' | 'medium' | 'high'; userMessage: string } {
    const errorMessage = error?.message || String(error);
    
    // Network errors
    if (errorMessage.includes('fetch') || errorMessage.includes('network') || errorMessage.includes('connection')) {
      return {
        type: 'network',
        severity: 'medium',
        userMessage: 'Connection issue. Retrying...',
      };
    }
    
    // Authentication errors
    if (errorMessage.includes('auth') || errorMessage.includes('unauthorized') || errorMessage.includes('token')) {
      return {
        type: 'auth',
        severity: 'high',
        userMessage: 'Authentication issue. Please refresh the page.',
      };
    }
    
    // Rate limiting
    if (errorMessage.includes('rate') || errorMessage.includes('limit') || errorMessage.includes('quota')) {
      return {
        type: 'rate_limit',
        severity: 'medium',
        userMessage: 'Too many requests. Please wait a moment.',
      };
    }
    
    // Real-time connection errors
    if (errorMessage.includes('realtime') || errorMessage.includes('websocket') || errorMessage.includes('subscription')) {
      return {
        type: 'realtime',
        severity: 'medium',
        userMessage: 'Real-time connection issue. Attempting to reconnect...',
      };
    }
    
    // Database errors
    if (errorMessage.includes('database') || errorMessage.includes('sql') || errorMessage.includes('relation')) {
      return {
        type: 'database',
        severity: 'high',
        userMessage: 'Database issue. Please try again later.',
      };
    }
    
    // Default
    return {
      type: 'unknown',
      severity: 'medium',
      userMessage: 'Something went wrong. Please try again.',
    };
  }

  /**
   * Show user-friendly error messages
   */
  private showUserMessage(errorInfo: any, context: ErrorContext): void {
    const { type, severity, userMessage } = errorInfo;
    
    // Don't show toast for low severity errors or during retries
    if (severity === 'low' || (context.retryCount && context.retryCount > 0)) {
      return;
    }
    
    const toastOptions = {
      duration: severity === 'high' ? 6000 : 4000,
      position: 'top-right' as const,
    };
    
    switch (severity) {
      case 'high':
        toast.error(userMessage, toastOptions);
        break;
      case 'medium':
        toast.error(userMessage, toastOptions);
        break;
      default:
        console.warn(`${context.operation} warning:`, userMessage);
    }
  }

  /**
   * Log errors for debugging
   */
  private logError(error: any, context: ErrorContext): void {
    const errorEntry: ErrorContext = {
      ...context,
      timestamp: new Date(),
    };
    
    this.errorLog.unshift(errorEntry);
    
    // Keep log size manageable
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }
    
    // Console logging for development
    if (process.env.NODE_ENV === 'development') {
      console.error(`[${context.operation}] Error:`, error);
      console.error('Context:', context);
    }
  }

  /**
   * Report errors to monitoring service
   */
  private reportError(error: any, context: ErrorContext): void {
    // This would integrate with error monitoring services like Sentry
    // For now, we'll just log to console in production
    if (process.env.NODE_ENV === 'production') {
      console.error('Production error:', {
        error: error?.message || String(error),
        context,
        timestamp: new Date().toISOString(),
        userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
      });
    }
  }

  /**
   * Retry operation with exponential backoff
   */
  async retryOperation<T>(
    operation: () => Promise<T>,
    context: ErrorContext,
    customConfig?: Partial<RetryConfig>
  ): Promise<T> {
    const config = { ...this.retryConfig, ...customConfig };
    let lastError: any;
    
    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        const result = await operation();
        
        // Success - reset any error state
        if (attempt > 0) {
          console.log(`✅ ${context.operation} succeeded after ${attempt} retries`);
        }
        
        return result;
      } catch (error) {
        lastError = error;
        
        // Don't retry on the last attempt
        if (attempt === config.maxRetries) {
          break;
        }
        
        // Calculate delay with exponential backoff
        const delay = Math.min(
          config.baseDelay * Math.pow(config.backoffMultiplier, attempt),
          config.maxDelay
        );
        
        console.warn(`⚠️  ${context.operation} failed (attempt ${attempt + 1}/${config.maxRetries + 1}). Retrying in ${delay}ms...`);
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    // All retries failed
    this.handleError(lastError, { ...context, retryCount: config.maxRetries });
    throw lastError;
  }

  /**
   * Check if operation should be retried
   */
  shouldRetry(error: any): boolean {
    const errorMessage = error?.message || String(error);
    
    // Don't retry authentication errors
    if (errorMessage.includes('auth') || errorMessage.includes('unauthorized')) {
      return false;
    }
    
    // Don't retry validation errors
    if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
      return false;
    }
    
    // Retry network and temporary errors
    return true;
  }

  /**
   * Get error statistics
   */
  getErrorStats(): { total: number; byType: Record<string, number>; recent: ErrorContext[] } {
    const byType: Record<string, number> = {};
    
    this.errorLog.forEach(entry => {
      const errorType = this.categorizeError(entry.data).type;
      byType[errorType] = (byType[errorType] || 0) + 1;
    });
    
    return {
      total: this.errorLog.length,
      byType,
      recent: this.errorLog.slice(0, 10),
    };
  }

  /**
   * Clear error log
   */
  clearErrorLog(): void {
    this.errorLog = [];
  }

  /**
   * Update retry configuration
   */
  updateRetryConfig(config: Partial<RetryConfig>): void {
    this.retryConfig = { ...this.retryConfig, ...config };
  }
}

// Export singleton instance
export const errorHandler = SupabaseErrorHandler.getInstance();

// Helper functions for common operations
export const withErrorHandling = async <T>(
  operation: () => Promise<T>,
  context: ErrorContext
): Promise<T | null> => {
  try {
    return await operation();
  } catch (error) {
    errorHandler.handleError(error, context);
    return null;
  }
};

export const withRetry = async <T>(
  operation: () => Promise<T>,
  context: ErrorContext,
  retryConfig?: Partial<RetryConfig>
): Promise<T | null> => {
  try {
    return await errorHandler.retryOperation(operation, context, retryConfig);
  } catch (error) {
    return null;
  }
};

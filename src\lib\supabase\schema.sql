-- Supabase Schema for Real-time Features Only
-- This is a minimal schema for real-time messaging and notifications
-- Primary data storage remains in MySQL

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Create messages_realtime table (minimal structure for real-time)
CREATE TABLE IF NOT EXISTS public.messages_realtime (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sender_id VARCHAR(255) NOT NULL,
    receiver_id VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    mysql_id VARCHAR(255), -- Reference to MySQL record
    
    -- Indexes for performance
    INDEX idx_messages_receiver (receiver_id, created_at),
    INDEX idx_messages_sender (sender_id, created_at),
    INDEX idx_messages_mysql (mysql_id)
);

-- Create notifications_realtime table (minimal structure for real-time)
CREATE TABLE IF NOT EXISTS public.notifications_realtime (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    recipient_id VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_status BOOLEAN DEFAULT FALSE,
    mysql_id VARCHAR(255), -- Reference to MySQL record
    
    -- Indexes for performance
    INDEX idx_notifications_recipient (recipient_id, created_at),
    INDEX idx_notifications_unread (recipient_id, read_status),
    INDEX idx_notifications_mysql (mysql_id)
);

-- Create user_presence table for online status
CREATE TABLE IF NOT EXISTS public.user_presence (
    user_id VARCHAR(255) PRIMARY KEY,
    status VARCHAR(20) DEFAULT 'offline' CHECK (status IN ('online', 'offline', 'away')),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    typing_in VARCHAR(255), -- conversation_id if typing
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security (RLS)
ALTER TABLE public.messages_realtime ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications_realtime ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_presence ENABLE ROW LEVEL SECURITY;

-- RLS Policies for messages_realtime
-- Users can only see messages they sent or received
CREATE POLICY "Users can view their own messages" ON public.messages_realtime
    FOR SELECT USING (
        auth.jwt() ->> 'sub' = sender_id OR 
        auth.jwt() ->> 'sub' = receiver_id
    );

CREATE POLICY "Users can insert their own messages" ON public.messages_realtime
    FOR INSERT WITH CHECK (auth.jwt() ->> 'sub' = sender_id);

-- RLS Policies for notifications_realtime
-- Users can only see their own notifications
CREATE POLICY "Users can view their own notifications" ON public.notifications_realtime
    FOR SELECT USING (auth.jwt() ->> 'sub' = recipient_id);

CREATE POLICY "System can insert notifications" ON public.notifications_realtime
    FOR INSERT WITH CHECK (true); -- Allow system to insert

CREATE POLICY "Users can update their notification read status" ON public.notifications_realtime
    FOR UPDATE USING (auth.jwt() ->> 'sub' = recipient_id);

-- RLS Policies for user_presence
-- Users can see all presence but only update their own
CREATE POLICY "Users can view all presence" ON public.user_presence
    FOR SELECT USING (true);

CREATE POLICY "Users can update their own presence" ON public.user_presence
    FOR INSERT WITH CHECK (auth.jwt() ->> 'sub' = user_id);

CREATE POLICY "Users can update their own presence status" ON public.user_presence
    FOR UPDATE USING (auth.jwt() ->> 'sub' = user_id);

-- Create functions for automatic cleanup
CREATE OR REPLACE FUNCTION cleanup_old_realtime_data()
RETURNS void AS $$
BEGIN
    -- Delete messages older than 30 days (they're in MySQL anyway)
    DELETE FROM public.messages_realtime 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    -- Delete read notifications older than 7 days
    DELETE FROM public.notifications_realtime 
    WHERE read_status = true AND created_at < NOW() - INTERVAL '7 days';
    
    -- Update offline users who haven't been seen for 5 minutes
    UPDATE public.user_presence 
    SET status = 'offline' 
    WHERE status != 'offline' AND last_seen < NOW() - INTERVAL '5 minutes';
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update user presence timestamp
CREATE OR REPLACE FUNCTION update_presence_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_presence_timestamp
    BEFORE UPDATE ON public.user_presence
    FOR EACH ROW
    EXECUTE FUNCTION update_presence_timestamp();

-- Schedule cleanup (run every hour)
-- Note: This requires pg_cron extension to be enabled in Supabase
-- SELECT cron.schedule('cleanup-realtime-data', '0 * * * *', 'SELECT cleanup_old_realtime_data();');

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

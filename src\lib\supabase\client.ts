/**
 * Supabase Client for Real-time Features
 * Handles real-time messaging and notifications only
 * Primary data operations remain with MySQL
 */

import { createClient, SupabaseClient, RealtimeChannel } from '@supabase/supabase-js';
import { supabaseConfig, SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY } from './config';

// Types for real-time data
export interface RealtimeMessage {
  id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  message_type: 'text' | 'image' | 'file';
  created_at: string;
  mysql_id?: string; // Reference to MySQL record
}

export interface RealtimeNotification {
  id: string;
  recipient_id: string;
  type: string;
  data: Record<string, any>;
  created_at: string;
  read_status: boolean;
  mysql_id?: string; // Reference to MySQL record
}

export interface UserPresence {
  user_id: string;
  status: 'online' | 'offline' | 'away';
  last_seen: string;
  typing_in?: string; // conversation_id if typing
}

// Client for public operations (frontend)
export const supabaseClient: SupabaseClient = createClient(
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  {
    auth: supabaseConfig.auth,
    realtime: {
      params: {
        eventsPerSecond: 10, // Rate limiting
      },
    },
  }
);

// Admin client for server-side operations
export const supabaseAdmin: SupabaseClient = createClient(
  SUPABASE_URL,
  SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

// Connection management
class SupabaseConnectionManager {
  private channels: Map<string, RealtimeChannel> = new Map();
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = supabaseConfig.realtime.maxReconnectAttempts;

  // Subscribe to real-time messages for a user
  subscribeToMessages(userId: string, callback: (message: RealtimeMessage) => void): RealtimeChannel {
    const channelName = `messages:${userId}`;
    
    if (this.channels.has(channelName)) {
      return this.channels.get(channelName)!;
    }

    const channel = supabaseClient
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: supabaseConfig.tables.messages,
          filter: `receiver_id=eq.${userId}`,
        },
        (payload) => {
          callback(payload.new as RealtimeMessage);
        }
      )
      .subscribe((status) => {
        console.log(`Messages subscription status for ${userId}:`, status);
        if (status === 'SUBSCRIBED') {
          this.reconnectAttempts = 0;
        }
      });

    this.channels.set(channelName, channel);
    return channel;
  }

  // Subscribe to real-time notifications for a user
  subscribeToNotifications(userId: string, callback: (notification: RealtimeNotification) => void): RealtimeChannel {
    const channelName = `notifications:${userId}`;
    
    if (this.channels.has(channelName)) {
      return this.channels.get(channelName)!;
    }

    const channel = supabaseClient
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: supabaseConfig.tables.notifications,
          filter: `recipient_id=eq.${userId}`,
        },
        (payload) => {
          callback(payload.new as RealtimeNotification);
        }
      )
      .subscribe((status) => {
        console.log(`Notifications subscription status for ${userId}:`, status);
      });

    this.channels.set(channelName, channel);
    return channel;
  }

  // Subscribe to user presence
  subscribeToPresence(userId: string, callback: (presence: UserPresence[]) => void): RealtimeChannel {
    const channelName = `presence:global`;
    
    if (this.channels.has(channelName)) {
      return this.channels.get(channelName)!;
    }

    const channel = supabaseClient
      .channel(channelName)
      .on('presence', { event: 'sync' }, () => {
        const state = channel.presenceState();
        const presenceList: UserPresence[] = Object.values(state).flat() as UserPresence[];
        callback(presenceList);
      })
      .subscribe();

    this.channels.set(channelName, channel);
    return channel;
  }

  // Unsubscribe from a channel
  unsubscribe(channelName: string): void {
    const channel = this.channels.get(channelName);
    if (channel) {
      supabaseClient.removeChannel(channel);
      this.channels.delete(channelName);
    }
  }

  // Unsubscribe from all channels
  unsubscribeAll(): void {
    this.channels.forEach((channel, channelName) => {
      supabaseClient.removeChannel(channel);
    });
    this.channels.clear();
  }

  // Get connection status
  getConnectionStatus(): string {
    return supabaseClient.realtime.connection?.readyState === 1 ? 'connected' : 'disconnected';
  }
}

// Export singleton instance
export const connectionManager = new SupabaseConnectionManager();

// Utility functions
export const isRealtimeEnabled = (): boolean => {
  return supabaseConfig.realtime.enabled;
};

export const getSupabaseStatus = () => {
  return {
    enabled: isRealtimeEnabled(),
    connected: connectionManager.getConnectionStatus() === 'connected',
    channels: connectionManager['channels'].size,
  };
};

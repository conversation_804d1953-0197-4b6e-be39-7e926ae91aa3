-- Supabase Schema for Real-time Features Only
-- Copy and paste this SQL in Supabase SQL Editor
-- This creates minimal tables for real-time messaging and notifications

-- Create messages_realtime table (minimal structure for real-time)
CREATE TABLE IF NOT EXISTS public.messages_realtime (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sender_id VARCHAR(255) NOT NULL,
    receiver_id VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    mysql_id VARCHAR(255) -- Reference to MySQL record
);

-- Create indexes for messages_realtime
CREATE INDEX IF NOT EXISTS idx_messages_receiver ON public.messages_realtime (receiver_id, created_at);
CREATE INDEX IF NOT EXISTS idx_messages_sender ON public.messages_realtime (sender_id, created_at);
CREATE INDEX IF NOT EXISTS idx_messages_mysql ON public.messages_realtime (mysql_id);

-- Create notifications_realtime table (minimal structure for real-time)
CREATE TABLE IF NOT EXISTS public.notifications_realtime (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    recipient_id VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_status BOOLEAN DEFAULT FALSE,
    mysql_id VARCHAR(255) -- Reference to MySQL record
);

-- Create indexes for notifications_realtime
CREATE INDEX IF NOT EXISTS idx_notifications_recipient ON public.notifications_realtime (recipient_id, created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_unread ON public.notifications_realtime (recipient_id, read_status);
CREATE INDEX IF NOT EXISTS idx_notifications_mysql ON public.notifications_realtime (mysql_id);

-- Create user_presence table for online status
CREATE TABLE IF NOT EXISTS public.user_presence (
    user_id VARCHAR(255) PRIMARY KEY,
    status VARCHAR(20) DEFAULT 'offline' CHECK (status IN ('online', 'offline', 'away')),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    typing_in VARCHAR(255), -- conversation_id if typing
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security (RLS)
ALTER TABLE public.messages_realtime ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications_realtime ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_presence ENABLE ROW LEVEL SECURITY;

-- RLS Policies for messages_realtime
-- Users can only see messages they sent or received
CREATE POLICY "Users can view their own messages" ON public.messages_realtime
    FOR SELECT USING (
        auth.jwt() ->> 'sub' = sender_id OR 
        auth.jwt() ->> 'sub' = receiver_id
    );

CREATE POLICY "Users can insert their own messages" ON public.messages_realtime
    FOR INSERT WITH CHECK (auth.jwt() ->> 'sub' = sender_id);

-- RLS Policies for notifications_realtime
-- Users can only see their own notifications
CREATE POLICY "Users can view their own notifications" ON public.notifications_realtime
    FOR SELECT USING (auth.jwt() ->> 'sub' = recipient_id);

CREATE POLICY "System can insert notifications" ON public.notifications_realtime
    FOR INSERT WITH CHECK (true); -- Allow system to insert

CREATE POLICY "Users can update their notification read status" ON public.notifications_realtime
    FOR UPDATE USING (auth.jwt() ->> 'sub' = recipient_id);

-- RLS Policies for user_presence
-- Users can see all presence but only update their own
CREATE POLICY "Users can view all presence" ON public.user_presence
    FOR SELECT USING (true);

CREATE POLICY "Users can update their own presence" ON public.user_presence
    FOR INSERT WITH CHECK (auth.jwt() ->> 'sub' = user_id);

CREATE POLICY "Users can update their own presence status" ON public.user_presence
    FOR UPDATE USING (auth.jwt() ->> 'sub' = user_id);

-- Create functions for automatic cleanup
CREATE OR REPLACE FUNCTION cleanup_old_realtime_data()
RETURNS void AS $$
BEGIN
    -- Delete messages older than 30 days (they're in MySQL anyway)
    DELETE FROM public.messages_realtime 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    -- Delete read notifications older than 7 days
    DELETE FROM public.notifications_realtime 
    WHERE read_status = true AND created_at < NOW() - INTERVAL '7 days';
    
    -- Update offline users who haven't been seen for 5 minutes
    UPDATE public.user_presence 
    SET status = 'offline' 
    WHERE status != 'offline' AND last_seen < NOW() - INTERVAL '5 minutes';
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update user presence timestamp
CREATE OR REPLACE FUNCTION update_presence_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_presence_timestamp
    BEFORE UPDATE ON public.user_presence
    FOR EACH ROW
    EXECUTE FUNCTION update_presence_timestamp();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Insert a test record to verify everything works
INSERT INTO public.user_presence (user_id, status) 
VALUES ('test-user-id', 'online') 
ON CONFLICT (user_id) DO UPDATE SET status = 'online';

-- Success message
SELECT 'Supabase real-time tables created successfully!' as message;
